import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface AnalysisOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  tokenCost: number;
  category: 'basic' | 'advanced' | 'premium';
}

interface InteractiveOptionsMenuProps {
  detectedTicker: string | null;
  userTokens: number;
  onOptionSelect: (option: AnalysisOption, ticker: string) => void;
}

const analysisOptions: AnalysisOption[] = [
  {
    id: 'price-check',
    title: '💰 Quick Price Check',
    description: 'See the current stock price, daily changes, and basic info. Perfect for beginners who just want to know "What\'s the price right now?"',
    icon: '💰',
    tokenCost: 10,
    category: 'basic'
  },
  {
    id: 'technical-analysis',
    title: '📈 Chart Analysis',
    description: 'Analyze price patterns and trends using charts. Shows if the stock is going up, down, or sideways based on technical indicators like RSI and moving averages.',
    icon: '📈',
    tokenCost: 50,
    category: 'advanced'
  },
  {
    id: 'sentiment-analysis',
    title: '🎭 What People Think',
    description: 'Find out what investors and news are saying about this stock. Are people excited or worried? This tracks social media buzz and news sentiment.',
    icon: '🎭',
    tokenCost: 40,
    category: 'advanced'
  },
  {
    id: 'fundamental-analysis',
    title: '📊 Company Health Check',
    description: 'Deep dive into the company\'s financial health. Look at profits, debt, and key ratios to see if the business is doing well financially.',
    icon: '📊',
    tokenCost: 60,
    category: 'advanced'
  },
  {
    id: 'investment-recommendation',
    title: '🎯 Should I Buy This?',
    description: 'Get a clear Buy, Hold, or Sell recommendation with simple explanations. Perfect when you need a straightforward answer about investing.',
    icon: '🎯',
    tokenCost: 75,
    category: 'premium'
  },
  {
    id: 'comprehensive-analysis',
    title: '📋 Complete Stock Report',
    description: 'Everything you need to know! Combines price, charts, sentiment, and company health into one detailed report. Best value for serious analysis.',
    icon: '📋',
    tokenCost: 150,
    category: 'premium'
  }
];

export const InteractiveOptionsMenu: React.FC<InteractiveOptionsMenuProps> = ({
  detectedTicker,
  userTokens,
  onOptionSelect
}) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-green-600 text-white border-green-500';
      case 'advanced': return 'bg-blue-600 text-white border-blue-500';
      case 'premium': return 'bg-purple-600 text-white border-purple-500';
      default: return 'bg-gray-600 text-white border-gray-500';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'basic': return 'BEGINNER FRIENDLY';
      case 'advanced': return 'INTERMEDIATE';
      case 'premium': return 'ADVANCED';
      default: return category.toUpperCase();
    }
  };

  const getCategoryDescription = (category: string) => {
    switch (category) {
      case 'basic': return 'Perfect for beginners - easy to understand';
      case 'advanced': return 'For users who want deeper insights';
      case 'premium': return 'Professional-level analysis and recommendations';
      default: return '';
    }
  };

  const canAfford = (tokenCost: number) => userTokens >= tokenCost;

  const handleOptionClick = (option: AnalysisOption) => {
    if (!detectedTicker) {
      alert('🔍 Please enter a stock ticker first!\n\nExamples: AAPL (Apple), MSFT (Microsoft), GOOGL (Google), TSLA (Tesla)\n\nJust type the company\'s stock symbol in the search box above.');
      return;
    }

    if (!canAfford(option.tokenCost)) {
      alert(`💳 Not enough tokens!\n\nYou need ${option.tokenCost} tokens for this analysis, but you only have ${userTokens} tokens.\n\nUpgrade your plan or wait for your tokens to refresh to continue.`);
      return;
    }

    setSelectedOption(option.id);
    onOptionSelect(option, detectedTicker);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-3 sm:p-4 lg:p-6">
      {/* Header Section */}
      <div className="text-center mb-6 sm:mb-8">
        <h2 className="text-2xl sm:text-3xl font-bold text-white mb-3">
          🚀 Choose Your Stock Analysis
        </h2>
        <p className="text-sm sm:text-base text-gray-300 mb-4 max-w-2xl mx-auto">
          Pick the type of analysis you want. Each option is designed to answer different questions about your stock.
        </p>
        {detectedTicker ? (
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-3">
            <span className="text-base sm:text-lg text-gray-300">Ready to analyze:</span>
            <Badge className="px-4 sm:px-6 py-2 sm:py-3 text-lg sm:text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg">
              📈 {detectedTicker}
            </Badge>
            <span className="text-sm text-green-400">✓ Stock found!</span>
          </div>
        ) : (
          <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-yellow-300 text-sm">
              👆 <strong>First step:</strong> Enter a stock ticker in the search box above
            </p>
            <p className="text-yellow-200 text-xs mt-1">
              Examples: AAPL, MSFT, GOOGL, TSLA, NVDA
            </p>
          </div>
        )}
      </div>

      {/* Category Legend */}
      <div className="mb-6 p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/20">
        <h3 className="text-sm font-semibold text-white mb-3 text-center">📚 Analysis Types Explained</h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-xs">
          <div className="flex items-center space-x-2">
            <Badge className="bg-green-600 text-white text-xs">BEGINNER FRIENDLY</Badge>
            <span className="text-gray-300">Easy to understand</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className="bg-blue-600 text-white text-xs">INTERMEDIATE</Badge>
            <span className="text-gray-300">More detailed insights</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className="bg-purple-600 text-white text-xs">ADVANCED</Badge>
            <span className="text-gray-300">Professional analysis</span>
          </div>
        </div>
      </div>

      {/* Options Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {analysisOptions.map((option) => {
          const affordable = canAfford(option.tokenCost);
          const isSelected = selectedOption === option.id;

          return (
            <Card
              key={option.id}
              className={`relative p-4 sm:p-6 cursor-pointer transition-all duration-300 hover:shadow-xl border-2 min-h-[220px] sm:min-h-[240px] touch-manipulation ${
                isSelected
                  ? 'border-blue-500 bg-blue-900/20 shadow-blue-500/20'
                  : affordable && detectedTicker
                    ? 'border-gray-600 hover:border-blue-400 hover:bg-white/5 active:scale-95'
                    : 'border-gray-700 opacity-60'
              }`}
              onClick={() => handleOptionClick(option)}
            >
              {/* Category Badge - Top Right */}
              <div className="flex justify-between items-start mb-3">
                <div className="text-2xl sm:text-3xl">{option.icon}</div>
                <div className="text-right">
                  <Badge className={`text-xs mb-1 ${getCategoryColor(option.category)}`}>
                    {getCategoryLabel(option.category)}
                  </Badge>
                  <p className="text-xs text-gray-400">{getCategoryDescription(option.category)}</p>
                </div>
              </div>

              {/* Title and Description */}
              <div className="mb-4 flex-1">
                <h3 className="text-lg sm:text-xl font-bold text-white mb-3 leading-tight">
                  {option.title}
                </h3>
                <p className="text-gray-300 text-sm sm:text-base leading-relaxed">
                  {option.description}
                </p>
              </div>

              {/* Token Cost and Action */}
              <div className="flex flex-col gap-3 mt-auto">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-400">💎 Cost:</span>
                    <Badge
                      variant={affordable ? 'default' : 'destructive'}
                      className={`text-xs ${affordable ? 'bg-green-600' : 'bg-red-600'} text-white`}
                    >
                      {option.tokenCost} tokens
                    </Badge>
                  </div>
                  {affordable && (
                    <span className="text-xs text-green-400">✓ You can afford this</span>
                  )}
                  {!affordable && (
                    <span className="text-xs text-red-400">⚠️ Need more tokens</span>
                  )}
                </div>

                <Button
                  size="sm"
                  disabled={!affordable || !detectedTicker}
                  className={`w-full text-sm font-semibold py-2 ${
                    affordable && detectedTicker
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg'
                      : 'bg-gray-600 text-gray-300 cursor-not-allowed'
                  }`}
                >
                  {!detectedTicker ? '🔍 Enter Stock Ticker First' :
                   !affordable ? '💳 Need More Tokens' :
                   '🚀 Start Analysis'}
                </Button>
              </div>

              {/* Loading State */}
              {isSelected && (
                <div className="absolute inset-0 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    <div className="text-center">
                      <span className="text-white font-medium text-sm">🔍 Analyzing {detectedTicker}...</span>
                      <p className="text-blue-200 text-xs mt-1">This may take a few seconds</p>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* Enhanced Token Status Footer */}
      <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm rounded-xl border border-white/20">
        <div className="flex flex-col space-y-4">
          {/* Token Balance */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-300">💎 Your Token Balance:</span>
              <Badge
                variant={userTokens > 100 ? 'default' : userTokens > 50 ? 'secondary' : 'destructive'}
                className={`text-sm px-3 py-1 ${
                  userTokens > 100 ? 'bg-green-600' :
                  userTokens > 50 ? 'bg-yellow-600' : 'bg-red-600'
                } text-white`}
              >
                {userTokens.toLocaleString()} tokens
              </Badge>
              {userTokens <= 50 && (
                <span className="text-xs text-red-400">⚠️ Running low!</span>
              )}
            </div>
            {userTokens > 100 && (
              <span className="text-xs text-green-400">✨ You're all set for multiple analyses!</span>
            )}
          </div>

          {/* Tips and Recommendations */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-xs">
            <div className="bg-blue-900/20 p-3 rounded-lg border border-blue-500/30">
              <h4 className="text-blue-300 font-semibold mb-1">💡 Pro Tip</h4>
              <p className="text-gray-300">Start with "Quick Price Check" if you're new to stock analysis!</p>
            </div>
            <div className="bg-purple-900/20 p-3 rounded-lg border border-purple-500/30">
              <h4 className="text-purple-300 font-semibold mb-1">🎯 Best Value</h4>
              <p className="text-gray-300">Comprehensive Report gives you everything in one analysis.</p>
            </div>
          </div>

          {/* Help Text */}
          <div className="text-center pt-2 border-t border-white/10">
            <p className="text-xs text-gray-400">
              🤔 <strong>New to investing?</strong> Try the beginner-friendly options first, then work your way up to advanced analysis.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
