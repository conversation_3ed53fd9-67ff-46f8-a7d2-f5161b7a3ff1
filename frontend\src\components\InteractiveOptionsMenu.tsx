import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface AnalysisOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  tokenCost: number;
  category: 'basic' | 'advanced' | 'premium';
}

interface InteractiveOptionsMenuProps {
  detectedTicker: string | null;
  userTokens: number;
  onOptionSelect: (option: AnalysisOption, ticker: string) => void;
}

const analysisOptions: AnalysisOption[] = [
  {
    id: 'price-check',
    title: 'Price Check',
    description: 'Get current stock price and basic market data',
    icon: '💰',
    tokenCost: 10,
    category: 'basic'
  },
  {
    id: 'technical-analysis',
    title: 'Technical Analysis',
    description: 'RSI, MACD, Moving Averages, Bollinger Bands',
    icon: '📈',
    tokenCost: 50,
    category: 'advanced'
  },
  {
    id: 'sentiment-analysis',
    title: 'Sentiment Analysis',
    description: 'Social media and news sentiment tracking',
    icon: '🎭',
    tokenCost: 40,
    category: 'advanced'
  },
  {
    id: 'fundamental-analysis',
    title: 'Fundamental Analysis',
    description: 'Financial health, P/E ratios, debt analysis',
    icon: '📊',
    tokenCost: 60,
    category: 'advanced'
  },
  {
    id: 'investment-recommendation',
    title: 'Investment Recommendation',
    description: 'Buy/Hold/Sell recommendation with reasoning',
    icon: '🎯',
    tokenCost: 75,
    category: 'premium'
  },
  {
    id: 'comprehensive-analysis',
    title: 'Comprehensive Report',
    description: 'Complete analysis with all indicators and insights',
    icon: '📋',
    tokenCost: 150,
    category: 'premium'
  }
];

export const InteractiveOptionsMenu: React.FC<InteractiveOptionsMenuProps> = ({
  detectedTicker,
  userTokens,
  onOptionSelect
}) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-green-600 text-white border-green-500';
      case 'advanced': return 'bg-blue-600 text-white border-blue-500';
      case 'premium': return 'bg-purple-600 text-white border-purple-500';
      default: return 'bg-gray-600 text-white border-gray-500';
    }
  };

  const canAfford = (tokenCost: number) => userTokens >= tokenCost;

  const handleOptionClick = (option: AnalysisOption) => {
    if (!detectedTicker) {
      alert('Please enter a stock ticker first (e.g., AAPL, MSFT, GOOGL)');
      return;
    }

    if (!canAfford(option.tokenCost)) {
      alert(`Insufficient tokens! You need ${option.tokenCost} tokens but only have ${userTokens}.`);
      return;
    }

    setSelectedOption(option.id);
    onOptionSelect(option, detectedTicker);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-3 sm:p-4 lg:p-6">
      {/* Header Section */}
      <div className="text-center mb-6 sm:mb-8">
        <h2 className="text-2xl sm:text-3xl font-bold text-white mb-2">
          Choose Your Analysis
        </h2>
        {detectedTicker ? (
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-2">
            <span className="text-base sm:text-lg text-gray-300">Analyzing:</span>
            <Badge className="px-3 sm:px-4 py-1.5 sm:py-2 text-base sm:text-lg font-bold bg-blue-600 text-white">
              {detectedTicker}
            </Badge>
          </div>
        ) : (
          <p className="text-sm sm:text-base text-gray-300">Enter a stock ticker to see analysis options</p>
        )}
      </div>

      {/* Options Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {analysisOptions.map((option) => {
          const affordable = canAfford(option.tokenCost);
          const isSelected = selectedOption === option.id;

          return (
            <Card
              key={option.id}
              className={`relative p-4 sm:p-6 cursor-pointer transition-all duration-200 hover:shadow-lg border-2 min-h-[180px] sm:min-h-[200px] touch-manipulation ${
                isSelected
                  ? 'border-blue-500 bg-blue-50'
                  : affordable
                    ? 'border-gray-200 hover:border-blue-300 active:scale-95'
                    : 'border-gray-200 opacity-60'
              }`}
              onClick={() => handleOptionClick(option)}
            >
              {/* Category Badge - Top Right */}
              <div className="flex justify-end mb-2">
                <Badge className={`text-xs ${getCategoryColor(option.category)}`}>
                  {option.category.toUpperCase()}
                </Badge>
              </div>

              {/* Icon and Title */}
              <div className="flex items-start space-x-3 sm:space-x-4 mb-4 flex-1">
                <div className="text-2xl sm:text-3xl shrink-0">{option.icon}</div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg sm:text-xl font-semibold text-white mb-2 leading-tight">
                    {option.title}
                  </h3>
                  <p className="text-gray-300 text-xs sm:text-sm leading-relaxed">
                    {option.description}
                  </p>
                </div>
              </div>

              {/* Token Cost and Action */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-2 mt-auto">
                <div className="flex items-center space-x-2">
                  <span className="text-xs sm:text-sm text-gray-500">Cost:</span>
                  <Badge variant={affordable ? 'default' : 'destructive'} className="text-xs">
                    {option.tokenCost} tokens
                  </Badge>
                </div>

                <Button
                  size="sm"
                  disabled={!affordable || !detectedTicker}
                  className={`w-full sm:w-auto text-xs sm:text-sm ${
                    affordable && detectedTicker
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {!detectedTicker ? 'Enter Ticker' : !affordable ? 'Need Tokens' : 'Analyze'}
                </Button>
              </div>

              {/* Loading State */}
              {isSelected && (
                <div className="absolute inset-0 bg-blue-50 bg-opacity-90 flex items-center justify-center rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 border-b-2 border-blue-600"></div>
                    <span className="text-blue-600 font-medium text-sm sm:text-base">Analyzing...</span>
                  </div>
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* Token Status Footer */}
      <div className="mt-6 sm:mt-8 p-3 sm:p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
          <div className="flex items-center space-x-2">
            <span className="text-xs sm:text-sm text-gray-300">Your Token Balance:</span>
            <Badge variant={userTokens > 100 ? 'default' : userTokens > 50 ? 'secondary' : 'destructive'} className="text-xs">
              {userTokens.toLocaleString()} tokens
            </Badge>
          </div>
          <div className="text-xs text-gray-400 text-left sm:text-right">
            💡 Tip: Comprehensive analysis provides the best value for detailed insights
          </div>
        </div>
      </div>
    </div>
  );
};
