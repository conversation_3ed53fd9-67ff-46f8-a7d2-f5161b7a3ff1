import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface AnalysisOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  tokenCost: number;
  category: 'basic' | 'advanced' | 'premium';
}

interface InteractiveOptionsMenuProps {
  detectedTicker: string | null;
  userTokens: number;
  onOptionSelect: (option: AnalysisOption, ticker: string) => void;
}

const analysisOptions: AnalysisOption[] = [
  {
    id: 'price-check',
    title: '💰 Quick Price Check',
    description: 'See the current stock price, daily changes, and basic info. Perfect for beginners who just want to know "What\'s the price right now?"',
    icon: '💰',
    tokenCost: 10,
    category: 'basic'
  },
  {
    id: 'technical-analysis',
    title: '📈 Chart Analysis',
    description: 'Analyze price patterns and trends using charts. Shows if the stock is going up, down, or sideways based on technical indicators like RSI and moving averages.',
    icon: '📈',
    tokenCost: 50,
    category: 'advanced'
  },
  {
    id: 'sentiment-analysis',
    title: '🎭 What People Think',
    description: 'Find out what investors and news are saying about this stock. Are people excited or worried? This tracks social media buzz and news sentiment.',
    icon: '🎭',
    tokenCost: 40,
    category: 'advanced'
  },
  {
    id: 'fundamental-analysis',
    title: '📊 Company Health Check',
    description: 'Deep dive into the company\'s financial health. Look at profits, debt, and key ratios to see if the business is doing well financially.',
    icon: '📊',
    tokenCost: 60,
    category: 'advanced'
  },
  {
    id: 'investment-recommendation',
    title: '🎯 Should I Buy This?',
    description: 'Get a clear Buy, Hold, or Sell recommendation with simple explanations. Perfect when you need a straightforward answer about investing.',
    icon: '🎯',
    tokenCost: 75,
    category: 'premium'
  },
  {
    id: 'comprehensive-analysis',
    title: '📋 Complete Stock Report',
    description: 'Everything you need to know! Combines price, charts, sentiment, and company health into one detailed report. Best value for serious analysis.',
    icon: '📋',
    tokenCost: 150,
    category: 'premium'
  }
];

export const InteractiveOptionsMenu: React.FC<InteractiveOptionsMenuProps> = ({
  detectedTicker,
  userTokens,
  onOptionSelect
}) => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'bg-green-600 text-white border-green-500';
      case 'advanced': return 'bg-blue-600 text-white border-blue-500';
      case 'premium': return 'bg-purple-600 text-white border-purple-500';
      default: return 'bg-gray-600 text-white border-gray-500';
    }
  };

  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'basic': return 'BEGINNER FRIENDLY';
      case 'advanced': return 'INTERMEDIATE';
      case 'premium': return 'ADVANCED';
      default: return category.toUpperCase();
    }
  };

  const getCategoryDescription = (category: string) => {
    switch (category) {
      case 'basic': return 'Perfect for beginners - easy to understand';
      case 'advanced': return 'For users who want deeper insights';
      case 'premium': return 'Professional-level analysis and recommendations';
      default: return '';
    }
  };

  const canAfford = (tokenCost: number) => userTokens >= tokenCost;

  const handleOptionClick = (option: AnalysisOption) => {
    if (!detectedTicker) {
      alert('🔍 Please enter a stock ticker first!\n\nExamples: AAPL (Apple), MSFT (Microsoft), GOOGL (Google), TSLA (Tesla)\n\nJust type the company\'s stock symbol in the search box above.');
      return;
    }

    if (!canAfford(option.tokenCost)) {
      alert(`💳 Not enough tokens!\n\nYou need ${option.tokenCost} tokens for this analysis, but you only have ${userTokens} tokens.\n\nUpgrade your plan or wait for your tokens to refresh to continue.`);
      return;
    }

    setSelectedOption(option.id);
    onOptionSelect(option, detectedTicker);
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-3 sm:p-4 lg:p-6">
      {/* Header Section */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-white mb-2">
          Choose Your Analysis
        </h2>
        {detectedTicker ? (
          <div className="flex items-center justify-center space-x-2">
            <span className="text-gray-300">Analyzing:</span>
            <Badge className="px-3 py-1 text-lg font-bold bg-blue-600 text-white">
              {detectedTicker}
            </Badge>
          </div>
        ) : (
          <p className="text-gray-300">Enter a stock ticker to see analysis options</p>
        )}
      </div>



      {/* Options Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {analysisOptions.map((option) => {
          const affordable = canAfford(option.tokenCost);
          const isSelected = selectedOption === option.id;

          return (
            <Card
              key={option.id}
              className={`relative p-6 cursor-pointer transition-all duration-200 hover:shadow-lg border min-h-[200px] ${
                isSelected
                  ? 'border-blue-500 bg-blue-50/10'
                  : affordable && detectedTicker
                    ? 'border-gray-300 hover:border-blue-300 hover:shadow-md'
                    : 'border-gray-600 opacity-50'
              }`}
              onClick={() => handleOptionClick(option)}
            >
              {/* Header with Icon and Badge */}
              <div className="flex items-start justify-between mb-4">
                <div className="text-3xl">{option.icon}</div>
                <Badge className={`text-xs ${getCategoryColor(option.category)}`}>
                  {getCategoryLabel(option.category)}
                </Badge>
              </div>

              {/* Title and Description */}
              <div className="mb-6 flex-1">
                <h3 className="text-lg font-semibold text-white mb-2">
                  {option.title}
                </h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {option.description}
                </p>
              </div>

              {/* Bottom Section */}
              <div className="flex items-center justify-between mt-auto">
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={affordable ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    {option.tokenCost} tokens
                  </Badge>
                  {affordable && detectedTicker && (
                    <span className="text-xs text-green-400">✓</span>
                  )}
                </div>

                <Button
                  size="sm"
                  disabled={!affordable || !detectedTicker}
                  className={`${
                    affordable && detectedTicker
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {!detectedTicker ? 'Enter Ticker' : !affordable ? 'Need Tokens' : 'Analyze'}
                </Button>
              </div>

              {/* Loading State */}
              {isSelected && (
                <div className="absolute inset-0 bg-blue-50/90 flex items-center justify-center rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                    <span className="text-blue-600 font-medium text-sm">Analyzing...</span>
                  </div>
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* Simple Token Status */}
      <div className="mt-8 p-4 bg-white/5 rounded-lg border border-white/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-300">Token Balance:</span>
            <Badge variant={userTokens > 100 ? 'default' : userTokens > 50 ? 'secondary' : 'destructive'}>
              {userTokens.toLocaleString()} tokens
            </Badge>
          </div>
          <div className="text-xs text-gray-400">
            💡 Start with Quick Price Check if you're new to investing
          </div>
        </div>
      </div>
    </div>
  );
};
