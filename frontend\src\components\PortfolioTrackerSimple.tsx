import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { HoldingsManager } from './HoldingsManager';
import { PerformanceDashboard } from './PerformanceDashboard';
import { BeginnerQuestionnaire } from './BeginnerQuestionnaire';
import { BeginnerTips } from './BeginnerTips';
import { portfolioApi, Portfolio, PortfolioHolding } from '../services/portfolioApi';
import {
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  BarChart3,
  Plus,
  User
} from 'lucide-react';

interface PortfolioTrackerProps {
  userTokens: number;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export interface UserProfile {
  experienceLevel: 'beginner' | 'intermediate' | 'advanced';
  investmentGoals: string[];
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  timeHorizon: 'short' | 'medium' | 'long';
  monthlyBudget: number;
  hasEmergencyFund: boolean;
  investmentKnowledge: string[];
  preferredSectors: string[];
}

export const PortfolioTracker: React.FC<PortfolioTrackerProps> = ({
  userTokens,
  userTier,
  onTokenDeduct
}) => {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeView, setActiveView] = useState<'overview' | 'holdings' | 'performance'>('overview');
  const [showQuestionnaire, setShowQuestionnaire] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Load portfolio data and check for user profile
  useEffect(() => {
    const loadPortfolios = async () => {
      try {
        setLoading(true);

        // Check if user has completed questionnaire
        const savedProfile = localStorage.getItem('userProfile');
        if (savedProfile) {
          setUserProfile(JSON.parse(savedProfile));
        } else {
          // Show questionnaire for new users
          setShowQuestionnaire(true);
        }

        // Use demo data for now
        const demoPortfolio = portfolioApi.getDemoPortfolio();
        setPortfolios([demoPortfolio]);
        setSelectedPortfolio(demoPortfolio);
      } catch (error) {
        console.error('Error loading portfolios:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPortfolios();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  const handleAddHolding = async (ticker: string, shares: number, price: number) => {
    // Mock implementation for demo
    console.log('Adding holding:', { ticker, shares, price });
  };

  const handleUpdateHolding = async (ticker: string, shares: number, price: number) => {
    // Mock implementation for demo
    console.log('Updating holding:', { ticker, shares, price });
  };

  const handleRemoveHolding = async (ticker: string) => {
    // Mock implementation for demo
    console.log('Removing holding:', ticker);
  };

  const handleQuestionnaireComplete = (profile: UserProfile) => {
    setUserProfile(profile);
    setShowQuestionnaire(false);
    localStorage.setItem('userProfile', JSON.stringify(profile));
  };

  const handleRetakeQuestionnaire = () => {
    setShowQuestionnaire(true);
  };

  // Show questionnaire for new users
  if (showQuestionnaire) {
    return (
      <BeginnerQuestionnaire
        onComplete={handleQuestionnaireComplete}
        onSkip={() => setShowQuestionnaire(false)}
      />
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading your portfolio...</p>
        </div>
      </div>
    );
  }

  if (!selectedPortfolio) {
    return (
      <div className="text-center py-12">
        <PieChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Portfolio Found</h3>
        <p className="text-gray-300 mb-6">Create your first portfolio to start tracking your investments.</p>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Create Portfolio
        </Button>
      </div>
    );
  }

  const viewTabs = [
    { id: 'overview', name: 'Overview', icon: DollarSign },
    { id: 'holdings', name: 'Holdings', icon: Target },
    { id: 'performance', name: 'Performance', icon: BarChart3 }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Main Portfolio Content */}
      <div className="lg:col-span-3 space-y-6">
        {/* Portfolio Header */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-white">{selectedPortfolio.name}</h2>
              <p className="text-gray-400 text-sm">
                Updated {new Date(selectedPortfolio.last_updated).toLocaleDateString()}
              </p>
            </div>
            <Badge className={`px-3 py-1 ${
              selectedPortfolio.total_gain_loss >= 0 ? 'bg-green-600' : 'bg-red-600'
            } text-white`}>
              {formatPercent(selectedPortfolio.total_gain_loss_percent)}
            </Badge>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
            {viewTabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveView(tab.id as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
                    activeView === tab.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </Card>



      {/* Portfolio Overview */}
      {activeView === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Portfolio Value</p>
                  <p className="text-xl font-bold text-white">
                    {formatCurrency(selectedPortfolio.total_value)}
                  </p>
                </div>
                <DollarSign className="h-5 w-5 text-blue-400" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Return</p>
                  <p className={`text-xl font-bold ${
                    selectedPortfolio.total_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatCurrency(selectedPortfolio.total_gain_loss)}
                  </p>
                  <p className={`text-xs ${
                    selectedPortfolio.total_gain_loss >= 0 ? 'text-green-300' : 'text-red-300'
                  }`}>
                    {formatPercent(selectedPortfolio.total_gain_loss_percent)}
                  </p>
                </div>
                {selectedPortfolio.total_gain_loss >= 0 ? (
                  <TrendingUp className="h-5 w-5 text-green-400" />
                ) : (
                  <TrendingDown className="h-5 w-5 text-red-400" />
                )}
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Holdings</p>
                  <p className="text-xl font-bold text-white">
                    {selectedPortfolio.holdings.length}
                  </p>
                  <p className="text-xs text-gray-400">
                    Investments
                  </p>
                </div>
                <BarChart3 className="h-5 w-5 text-blue-400" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Cash Balance</p>
                  <p className="text-xl font-bold text-white">
                    {formatCurrency(selectedPortfolio.cash_balance)}
                  </p>
                  <p className="text-xs text-gray-400">
                    Available
                  </p>
                </div>
                <DollarSign className="h-5 w-5 text-green-400" />
              </div>
            </Card>
          </div>

          {/* Portfolio Allocation */}
          {selectedPortfolio.holdings.length > 0 && (
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Portfolio Allocation</h3>
              <div className="space-y-3">
                {selectedPortfolio.holdings.map((holding, index) => {
                  const percentage = (holding.market_value / selectedPortfolio.total_value) * 100;
                  return (
                    <div key={holding.ticker} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-blue-500 rounded-full" />
                          <span className="text-white font-medium">{holding.ticker}</span>
                          <span className="text-gray-400 text-sm">{holding.company_name}</span>
                        </div>
                        <div className="text-right">
                          <span className="text-white font-semibold text-sm">
                            {formatCurrency(holding.market_value)}
                          </span>
                          <span className="text-gray-400 text-xs ml-2">
                            {percentage.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 bg-blue-500 rounded-full transition-all duration-300"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Holdings View */}
      {activeView === 'holdings' && (
        <HoldingsManager
          holdings={selectedPortfolio.holdings}
          onAddHolding={handleAddHolding}
          onUpdateHolding={handleUpdateHolding}
          onRemoveHolding={handleRemoveHolding}
          userTier={userTier}
        />
      )}

      {/* Performance View */}
      {activeView === 'performance' && (
        <PerformanceDashboard
          portfolio={selectedPortfolio}
          userTier={userTier}
          onTokenDeduct={onTokenDeduct}
        />
      )}

      {/* Holdings List for Overview */}
      {activeView === 'overview' && (
        <Card className="p-6">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-white flex items-center">
              <Target className="h-5 w-5 mr-2 text-blue-400" />
              Your Holdings
            </h3>
          </div>

          {selectedPortfolio.holdings.length === 0 && (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-gray-400" />
              </div>
              <h4 className="text-white font-medium mb-2">No Holdings Yet</h4>
              <p className="text-gray-400 text-sm">
                Your portfolio is empty. Holdings will appear here once you add investments.
              </p>
            </div>
          )}

          <div className="space-y-3">
            {selectedPortfolio.holdings.map((holding) => (
              <div key={holding.ticker} className="flex items-center justify-between p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold">{holding.ticker}</span>
                  </div>
                  <div>
                    <p className="text-white font-medium">{holding.company_name}</p>
                    <p className="text-gray-400 text-sm">
                      {holding.shares} shares @ {formatCurrency(holding.average_cost)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">{formatCurrency(holding.market_value)}</p>
                  <p className={`text-sm ${
                    holding.unrealized_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatCurrency(holding.unrealized_gain_loss)} ({formatPercent(holding.unrealized_gain_loss_percent)})
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="sticky top-6 space-y-6">
          {/* User Profile Summary */}
          {userProfile && (
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-semibold">Your Profile</h3>
                <Button
                  onClick={handleRetakeQuestionnaire}
                  variant="outline"
                  size="sm"
                  className="text-xs"
                >
                  Edit
                </Button>
              </div>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Experience:</span>
                  <span className="text-white capitalize">{userProfile.experienceLevel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Risk Level:</span>
                  <span className={`capitalize ${
                    userProfile.riskTolerance === 'conservative' ? 'text-green-400' :
                    userProfile.riskTolerance === 'moderate' ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {userProfile.riskTolerance}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Budget:</span>
                  <span className="text-white">${userProfile.monthlyBudget}/mo</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Timeline:</span>
                  <span className="text-white capitalize">{userProfile.timeHorizon}-term</span>
                </div>
              </div>
            </Card>
          )}

          {/* Quick Actions */}
          <Card className="p-4">
            <h3 className="text-white font-semibold mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <Button
                onClick={() => setActiveView('performance')}
                variant="outline"
                className="w-full"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                View Performance
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};
