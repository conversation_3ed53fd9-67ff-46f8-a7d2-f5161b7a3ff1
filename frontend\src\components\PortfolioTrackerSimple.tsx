import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { HoldingsManager } from './HoldingsManager';
import { PerformanceDashboard } from './PerformanceDashboard';
import { BeginnerQuestionnaire } from './BeginnerQuestionnaire';
import { BeginnerTips } from './BeginnerTips';
import { portfolioApi, Portfolio, PortfolioHolding } from '../services/portfolioApi';
import {
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  BarChart3,
  Plus,
  User
} from 'lucide-react';

interface PortfolioTrackerProps {
  userTokens: number;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export interface UserProfile {
  experienceLevel: 'beginner' | 'intermediate' | 'advanced';
  investmentGoals: string[];
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  timeHorizon: 'short' | 'medium' | 'long';
  monthlyBudget: number;
  hasEmergencyFund: boolean;
  investmentKnowledge: string[];
  preferredSectors: string[];
}

export const PortfolioTracker: React.FC<PortfolioTrackerProps> = ({
  userTokens,
  userTier,
  onTokenDeduct
}) => {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeView, setActiveView] = useState<'overview' | 'holdings' | 'performance'>('overview');
  const [showQuestionnaire, setShowQuestionnaire] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Load portfolio data and check for user profile
  useEffect(() => {
    const loadPortfolios = async () => {
      try {
        setLoading(true);

        // Check if user has completed questionnaire
        const savedProfile = localStorage.getItem('userProfile');
        if (savedProfile) {
          setUserProfile(JSON.parse(savedProfile));
        } else {
          // Show questionnaire for new users
          setShowQuestionnaire(true);
        }

        // Use demo data for now
        const demoPortfolio = portfolioApi.getDemoPortfolio();
        setPortfolios([demoPortfolio]);
        setSelectedPortfolio(demoPortfolio);
      } catch (error) {
        console.error('Error loading portfolios:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPortfolios();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  const handleAddHolding = async (ticker: string, shares: number, price: number) => {
    // Mock implementation for demo
    console.log('Adding holding:', { ticker, shares, price });
  };

  const handleUpdateHolding = async (ticker: string, shares: number, price: number) => {
    // Mock implementation for demo
    console.log('Updating holding:', { ticker, shares, price });
  };

  const handleRemoveHolding = async (ticker: string) => {
    // Mock implementation for demo
    console.log('Removing holding:', ticker);
  };

  const handleQuestionnaireComplete = (profile: UserProfile) => {
    setUserProfile(profile);
    setShowQuestionnaire(false);
    localStorage.setItem('userProfile', JSON.stringify(profile));
  };

  const handleRetakeQuestionnaire = () => {
    setShowQuestionnaire(true);
  };

  // Show questionnaire for new users
  if (showQuestionnaire) {
    return (
      <BeginnerQuestionnaire
        onComplete={handleQuestionnaireComplete}
        onSkip={() => setShowQuestionnaire(false)}
      />
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading your portfolio...</p>
        </div>
      </div>
    );
  }

  if (!selectedPortfolio) {
    return (
      <div className="text-center py-12">
        <PieChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Portfolio Found</h3>
        <p className="text-gray-300 mb-6">Create your first portfolio to start tracking your investments.</p>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Create Portfolio
        </Button>
      </div>
    );
  }

  const viewTabs = [
    { id: 'overview', name: 'Overview', icon: DollarSign },
    { id: 'holdings', name: 'Holdings', icon: Target },
    { id: 'performance', name: 'Performance', icon: BarChart3 }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Main Portfolio Content */}
      <div className="lg:col-span-3 space-y-6">
        {/* Portfolio Header with Tabs */}
        <Card className="p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">{selectedPortfolio.name}</h2>
            <p className="text-gray-300">
              Last updated: {new Date(selectedPortfolio.last_updated).toLocaleDateString()}
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <Badge className={`${
              selectedPortfolio.total_gain_loss >= 0 ? 'bg-green-600' : 'bg-red-600'
            } text-white px-3 py-1`}>
              {formatPercent(selectedPortfolio.total_gain_loss_percent)}
            </Badge>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
          {viewTabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
                  activeView === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </div>
      </Card>

      {/* Personalized Welcome Message for Beginners */}
      {userProfile && userProfile.experienceLevel === 'beginner' && activeView === 'overview' && (
        <Card className="p-6 bg-gradient-to-r from-green-900/20 to-blue-900/20 border-green-500/30 mb-6">
          <div className="flex items-start space-x-4">
            <div className="p-3 bg-green-600 rounded-full">
              <User className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-white mb-2">
                Welcome to Your Investment Journey! 🌱
              </h3>
              <p className="text-gray-300 mb-3">
                Great job taking the first step! Based on your profile, here are some personalized tips:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="p-3 bg-white/5 rounded-lg">
                  <h4 className="text-green-300 font-medium text-sm">📚 Learning Focus</h4>
                  <p className="text-gray-300 text-xs mt-1">
                    {userProfile.investmentKnowledge.length > 0
                      ? `Start with ${userProfile.investmentKnowledge[0]} basics`
                      : 'Begin with ETFs and index funds - they\'re perfect for beginners!'
                    }
                  </p>
                </div>
                <div className="p-3 bg-white/5 rounded-lg">
                  <h4 className="text-blue-300 font-medium text-sm">💰 Budget Strategy</h4>
                  <p className="text-gray-300 text-xs mt-1">
                    With ${userProfile.monthlyBudget}/month, consider dollar-cost averaging into diversified funds
                  </p>
                </div>
                <div className="p-3 bg-white/5 rounded-lg">
                  <h4 className="text-purple-300 font-medium text-sm">🎯 Goal Focus</h4>
                  <p className="text-gray-300 text-xs mt-1">
                    {userProfile.investmentGoals.length > 0
                      ? `Prioritizing ${userProfile.investmentGoals[0].replace('_', ' ')}`
                      : 'Focus on building a solid foundation first'
                    }
                  </p>
                </div>
                <div className="p-3 bg-white/5 rounded-lg">
                  <h4 className="text-yellow-300 font-medium text-sm">⏰ Timeline</h4>
                  <p className="text-gray-300 text-xs mt-1">
                    {userProfile.timeHorizon === 'long'
                      ? 'Great! Long-term investing gives you more opportunities'
                      : 'Consider your timeline when choosing investments'
                    }
                  </p>
                </div>
              </div>
              <div className="mt-4 flex space-x-2">
                <Button
                  onClick={handleRetakeQuestionnaire}
                  variant="outline"
                  size="sm"
                  className="border-green-500 text-green-300 hover:bg-green-500/10"
                >
                  Update Profile
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Portfolio Overview */}
      {activeView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-6 bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/30">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Portfolio Value</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(selectedPortfolio.total_value)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-400" />
            </div>
          </Card>

          <Card className={`p-6 ${
            selectedPortfolio.total_gain_loss >= 0 
              ? 'bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/30'
              : 'bg-gradient-to-br from-red-900/20 to-rose-900/20 border-red-500/30'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Gain/Loss</p>
                <p className={`text-2xl font-bold ${
                  selectedPortfolio.total_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatCurrency(selectedPortfolio.total_gain_loss)}
                </p>
                <p className={`text-sm ${
                  selectedPortfolio.total_gain_loss >= 0 ? 'text-green-300' : 'text-red-300'
                }`}>
                  {formatPercent(selectedPortfolio.total_gain_loss_percent)}
                </p>
              </div>
              {selectedPortfolio.total_gain_loss >= 0 ? (
                <TrendingUp className="h-8 w-8 text-green-400" />
              ) : (
                <TrendingDown className="h-8 w-8 text-red-400" />
              )}
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-br from-purple-900/20 to-pink-900/20 border-purple-500/30">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Holdings Count</p>
                <p className="text-2xl font-bold text-white">
                  {selectedPortfolio.holdings.length}
                </p>
                <p className="text-sm text-gray-300">
                  Cash: {formatCurrency(selectedPortfolio.cash_balance)}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-400" />
            </div>
          </Card>
        </div>
      )}

      {/* Holdings View */}
      {activeView === 'holdings' && (
        <HoldingsManager
          holdings={selectedPortfolio.holdings}
          onAddHolding={handleAddHolding}
          onUpdateHolding={handleUpdateHolding}
          onRemoveHolding={handleRemoveHolding}
          userTier={userTier}
        />
      )}

      {/* Performance View */}
      {activeView === 'performance' && (
        <PerformanceDashboard
          portfolio={selectedPortfolio}
          userTier={userTier}
          onTokenDeduct={onTokenDeduct}
        />
      )}

      {/* Holdings List for Overview */}
      {activeView === 'overview' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-white flex items-center">
              <Target className="h-5 w-5 mr-2 text-blue-400" />
              Holdings Overview
            </h3>
            {userProfile?.experienceLevel === 'beginner' && (
              <div className="text-xs text-gray-400">
                💡 These are the stocks/funds you own
              </div>
            )}
          </div>

          {userProfile?.experienceLevel === 'beginner' && selectedPortfolio.holdings.length === 0 && (
            <div className="text-center py-8 bg-blue-900/10 rounded-lg border border-blue-500/20">
              <div className="mb-4">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Plus className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-white font-semibold mb-2">Ready to Start Investing?</h4>
                <p className="text-gray-300 text-sm mb-4 max-w-md mx-auto">
                  As a beginner, we recommend starting with broad market ETFs like SPY or VTI.
                  These give you instant diversification across hundreds of companies!
                </p>
                <div className="space-y-2 text-xs text-gray-400 max-w-sm mx-auto">
                  <div className="flex items-center justify-center space-x-2">
                    <span>📈</span>
                    <span>ETFs = Exchange Traded Funds (like a basket of stocks)</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <span>🛡️</span>
                    <span>Diversification = Don't put all eggs in one basket</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-3">
            {selectedPortfolio.holdings.map((holding) => (
              <div key={holding.ticker} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{holding.ticker}</span>
                  </div>
                  <div>
                    <p className="text-white font-medium">{holding.company_name}</p>
                    <p className="text-gray-300 text-sm">
                      {holding.shares} shares @ {formatCurrency(holding.average_cost)}
                      {userProfile?.experienceLevel === 'beginner' && (
                        <span className="text-xs text-blue-300 ml-2">
                          (Your average purchase price)
                        </span>
                      )}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">{formatCurrency(holding.market_value)}</p>
                  <p className={`text-sm ${
                    holding.unrealized_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatCurrency(holding.unrealized_gain_loss)} ({formatPercent(holding.unrealized_gain_loss_percent)})
                    {userProfile?.experienceLevel === 'beginner' && (
                      <span className="block text-xs text-gray-400">
                        {holding.unrealized_gain_loss >= 0 ? 'Paper gain' : 'Paper loss'}
                      </span>
                    )}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {userProfile?.experienceLevel === 'beginner' && selectedPortfolio.holdings.length > 0 && (
            <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
              <div className="flex items-start space-x-2">
                <span className="text-yellow-400">💡</span>
                <div>
                  <p className="text-yellow-200 text-sm font-medium">Beginner Tip:</p>
                  <p className="text-yellow-100 text-xs mt-1">
                    "Paper gains/losses" mean unrealized - you only actually gain or lose money when you sell.
                    Stay focused on your long-term goals and don't panic over short-term fluctuations!
                  </p>
                </div>
              </div>
            </div>
          )}
        </Card>
      )}
      </div>

      {/* Sidebar with Beginner Tips */}
      <div className="lg:col-span-1">
        <div className="sticky top-6 space-y-4">
          <BeginnerTips
            currentView={activeView}
            userProfile={userProfile}
          />

          {/* Quick Actions for Beginners */}
          {userProfile?.experienceLevel === 'beginner' && (
            <Card className="p-4 bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/30">
              <h3 className="text-white font-semibold text-sm mb-3 flex items-center">
                <Target className="h-4 w-4 mr-2 text-green-400" />
                Quick Start Guide
              </h3>
              <div className="space-y-2 text-xs">
                <div className="flex items-center space-x-2 text-gray-300">
                  <span className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center text-white text-xs">1</span>
                  <span>Set up emergency fund first</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-300">
                  <span className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs">2</span>
                  <span>Start with broad market ETFs</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-300">
                  <span className="w-5 h-5 bg-purple-600 rounded-full flex items-center justify-center text-white text-xs">3</span>
                  <span>Invest regularly (dollar-cost averaging)</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-300">
                  <span className="w-5 h-5 bg-yellow-600 rounded-full flex items-center justify-center text-white text-xs">4</span>
                  <span>Stay patient and consistent</span>
                </div>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-600">
                <p className="text-xs text-gray-400 mb-2">Recommended first investments:</p>
                <div className="space-y-1">
                  <div className="text-xs text-green-300">• VTI - Total Stock Market ETF</div>
                  <div className="text-xs text-blue-300">• SPY - S&P 500 ETF</div>
                  <div className="text-xs text-purple-300">• BND - Bond Market ETF</div>
                </div>
              </div>
            </Card>
          )}

          {/* Risk Level Indicator */}
          {userProfile && (
            <Card className="p-4">
              <h3 className="text-white font-semibold text-sm mb-2">Your Profile</h3>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-300">Experience:</span>
                  <span className="text-blue-300 capitalize">{userProfile.experienceLevel}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Risk Tolerance:</span>
                  <span className={`capitalize ${
                    userProfile.riskTolerance === 'conservative' ? 'text-green-300' :
                    userProfile.riskTolerance === 'moderate' ? 'text-yellow-300' : 'text-red-300'
                  }`}>
                    {userProfile.riskTolerance}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Monthly Budget:</span>
                  <span className="text-green-300">${userProfile.monthlyBudget}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300">Timeline:</span>
                  <span className="text-purple-300 capitalize">{userProfile.timeHorizon}-term</span>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
