import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  X,
  AlertCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface HoldingFormData {
  ticker: string;
  shares: string;
  price: string;
}

interface HoldingsManagerProps {
  holdings: PortfolioHolding[];
  onAddHolding: (ticker: string, shares: number, price: number) => void;
  onUpdateHolding: (ticker: string, shares: number, price: number) => void;
  onRemoveHolding: (ticker: string) => void;
  userTier: 'Basic' | 'Pro';
}

export const HoldingsManager: React.FC<HoldingsManagerProps> = ({
  holdings,
  onAddHolding,
  onUpdateHolding,
  onRemoveHolding,
  userTier
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTicker, setEditingTicker] = useState<string | null>(null);
  const [formData, setFormData] = useState<HoldingFormData>({
    ticker: '',
    shares: '',
    price: ''
  });
  const [formErrors, setFormErrors] = useState<Partial<HoldingFormData>>({});

  const validateForm = (data: HoldingFormData): boolean => {
    const errors: Partial<HoldingFormData> = {};

    if (!data.ticker.trim()) {
      errors.ticker = 'Ticker symbol is required';
    } else if (!/^[A-Z]{1,5}$/.test(data.ticker.trim())) {
      errors.ticker = 'Invalid ticker format (1-5 uppercase letters)';
    }

    if (!data.shares.trim()) {
      errors.shares = 'Number of shares is required';
    } else if (isNaN(Number(data.shares)) || Number(data.shares) <= 0) {
      errors.shares = 'Must be a positive number';
    }

    if (!data.price.trim()) {
      errors.price = 'Price per share is required';
    } else if (isNaN(Number(data.price)) || Number(data.price) <= 0) {
      errors.price = 'Must be a positive number';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm(formData)) {
      return;
    }

    const ticker = formData.ticker.toUpperCase();
    const shares = parseFloat(formData.shares);
    const price = parseFloat(formData.price);

    if (editingTicker) {
      onUpdateHolding(ticker, shares, price);
      setEditingTicker(null);
    } else {
      onAddHolding(ticker, shares, price);
      setShowAddForm(false);
    }

    resetForm();
  };

  const resetForm = () => {
    setFormData({ ticker: '', shares: '', price: '' });
    setFormErrors({});
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingTicker(null);
    resetForm();
  };

  const handleEdit = (holding: PortfolioHolding) => {
    setFormData({
      ticker: holding.ticker,
      shares: holding.shares.toString(),
      price: holding.average_cost.toString()
    });
    setEditingTicker(holding.ticker);
    setShowAddForm(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Portfolio Holdings</h3>
        <Button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
          disabled={showAddForm || editingTicker !== null}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Holding
        </Button>
      </div>

      {/* Add/Edit Holding Form */}
      {(showAddForm || editingTicker) && (
        <div className="mb-6 p-4 bg-white/5 rounded-lg border border-white/20">
          <h4 className="text-lg font-medium text-white mb-4">
            {editingTicker ? `Edit ${editingTicker}` : 'Add New Holding'}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Input
                placeholder="Ticker (e.g., AAPL)"
                value={formData.ticker}
                onChange={(e) => setFormData({...formData, ticker: e.target.value.toUpperCase()})}
                className={`text-white ${formErrors.ticker ? 'border-red-500' : ''}`}
                disabled={editingTicker !== null}
              />
              {formErrors.ticker && (
                <p className="text-red-400 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {formErrors.ticker}
                </p>
              )}
            </div>
            <div>
              <Input
                type="number"
                placeholder="Number of shares"
                value={formData.shares}
                onChange={(e) => setFormData({...formData, shares: e.target.value})}
                className={`text-white ${formErrors.shares ? 'border-red-500' : ''}`}
              />
              {formErrors.shares && (
                <p className="text-red-400 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {formErrors.shares}
                </p>
              )}
            </div>
            <div>
              <Input
                type="number"
                step="0.01"
                placeholder="Price per share"
                value={formData.price}
                onChange={(e) => setFormData({...formData, price: e.target.value})}
                className={`text-white ${formErrors.price ? 'border-red-500' : ''}`}
              />
              {formErrors.price && (
                <p className="text-red-400 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  {formErrors.price}
                </p>
              )}
            </div>
          </div>
          <div className="flex gap-2 mt-4">
            <Button
              onClick={handleSubmit}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              {editingTicker ? 'Update' : 'Add'} Holding
            </Button>
            <Button
              onClick={handleCancel}
              variant="outline"
              className="border-gray-500 text-gray-300 hover:bg-gray-700"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </div>
        </div>
      )}

      {/* Holdings Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-white/20">
              <th className="text-left py-3 px-2 text-gray-300 font-medium">Symbol</th>
              <th className="text-left py-3 px-2 text-gray-300 font-medium">Company</th>
              <th className="text-right py-3 px-2 text-gray-300 font-medium">Shares</th>
              <th className="text-right py-3 px-2 text-gray-300 font-medium">Avg Cost</th>
              <th className="text-right py-3 px-2 text-gray-300 font-medium">Current</th>
              <th className="text-right py-3 px-2 text-gray-300 font-medium">Market Value</th>
              <th className="text-right py-3 px-2 text-gray-300 font-medium">Gain/Loss</th>
              <th className="text-center py-3 px-2 text-gray-300 font-medium">Actions</th>
            </tr>
          </thead>
          <tbody>
            {holdings.map((holding) => (
              <tr 
                key={holding.ticker} 
                className={`border-b border-white/10 hover:bg-white/5 transition-colors ${
                  editingTicker === holding.ticker ? 'bg-blue-900/20 border-blue-500/30' : ''
                }`}
              >
                <td className="py-4 px-2">
                  <div className="flex items-center">
                    <span className="font-semibold text-white">{holding.ticker}</span>
                    <Badge className="ml-2 bg-gray-600 text-gray-200 text-xs">
                      {holding.sector}
                    </Badge>
                  </div>
                </td>
                <td className="py-4 px-2 text-gray-300 text-sm">
                  {holding.company_name}
                </td>
                <td className="py-4 px-2 text-right text-white">
                  {holding.shares.toLocaleString()}
                </td>
                <td className="py-4 px-2 text-right text-gray-300">
                  {formatCurrency(holding.average_cost)}
                </td>
                <td className="py-4 px-2 text-right text-white">
                  {formatCurrency(holding.current_price)}
                </td>
                <td className="py-4 px-2 text-right text-white font-medium">
                  {formatCurrency(holding.market_value)}
                </td>
                <td className={`py-4 px-2 text-right font-medium ${
                  holding.unrealized_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  <div className="flex items-center justify-end space-x-1">
                    {holding.unrealized_gain_loss >= 0 ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    <div>
                      <div>{formatCurrency(holding.unrealized_gain_loss)}</div>
                      <div className="text-xs">
                        {formatPercent(holding.unrealized_gain_loss_percent)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="py-4 px-2 text-center">
                  <div className="flex justify-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(holding)}
                      disabled={showAddForm || editingTicker !== null}
                      className="border-gray-500 text-gray-300 hover:bg-gray-700"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onRemoveHolding(holding.ticker)}
                      disabled={showAddForm || editingTicker !== null}
                      className="border-red-500 text-red-400 hover:bg-red-900/20"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {holdings.length === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">No holdings in your portfolio yet</div>
          <p className="text-gray-500 text-sm">
            Add your first stock holding to start tracking your investments
          </p>
        </div>
      )}
    </Card>
  );
};
