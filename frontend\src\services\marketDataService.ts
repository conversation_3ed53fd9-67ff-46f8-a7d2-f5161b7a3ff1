// Real-time Market Data Service
export interface StockData {
  ticker: string;
  currentPrice: number;
  priceChange: number;
  priceChangePercent: number;
  volume: number;
  marketCap: string;
  peRatio: number;
  dividend: number;
  high52Week: number;
  low52Week: number;
  openPrice: number;
  previousClose: number;
  dayHigh: number;
  dayLow: number;
  avgVolume: number;
  beta: number;
  eps: number;
  lastUpdated: string;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  sma20: number;
  sma50: number;
  sma200: number;
  ema12: number;
  ema26: number;
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
  stochastic: {
    k: number;
    d: number;
  };
}

export interface FundamentalData {
  revenue: number;
  netIncome: number;
  totalAssets: number;
  totalDebt: number;
  freeCashFlow: number;
  returnOnEquity: number;
  returnOnAssets: number;
  debtToEquity: number;
  currentRatio: number;
  quickRatio: number;
  grossMargin: number;
  operatingMargin: number;
  profitMargin: number;
  bookValue: number;
  priceToBook: number;
  priceToSales: number;
  pegRatio: number;
  dividendYield: number;
  payoutRatio: number;
  earningsGrowth: number;
  revenueGrowth: number;
}

export interface NewsItem {
  title: string;
  summary: string;
  url: string;
  source: string;
  publishedAt: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  relevanceScore: number;
}

export interface AnalystRating {
  firm: string;
  rating: 'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell';
  targetPrice: number;
  date: string;
}

class MarketDataService {
  private baseUrl = 'http://localhost:8000/api';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 60000; // 1 minute cache

  private isValidTicker(ticker: string): boolean {
    return /^[A-Z]{1,5}$/.test(ticker.toUpperCase());
  }

  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Generate realistic mock data for development
  private generateMockStockData(ticker: string): StockData {
    const basePrice = Math.random() * 200 + 50; // $50-$250
    const change = (Math.random() - 0.5) * 10; // -$5 to +$5
    const changePercent = (change / basePrice) * 100;
    
    return {
      ticker: ticker.toUpperCase(),
      currentPrice: Number(basePrice.toFixed(2)),
      priceChange: Number(change.toFixed(2)),
      priceChangePercent: Number(changePercent.toFixed(2)),
      volume: Math.floor(Math.random() * 10000000) + 1000000,
      marketCap: `$${(Math.random() * 500 + 10).toFixed(1)}B`,
      peRatio: Number((Math.random() * 30 + 10).toFixed(2)),
      dividend: Number((Math.random() * 5).toFixed(2)),
      high52Week: Number((basePrice * (1 + Math.random() * 0.5)).toFixed(2)),
      low52Week: Number((basePrice * (1 - Math.random() * 0.3)).toFixed(2)),
      openPrice: Number((basePrice + (Math.random() - 0.5) * 2).toFixed(2)),
      previousClose: Number((basePrice - change).toFixed(2)),
      dayHigh: Number((basePrice + Math.random() * 3).toFixed(2)),
      dayLow: Number((basePrice - Math.random() * 3).toFixed(2)),
      avgVolume: Math.floor(Math.random() * 5000000) + 2000000,
      beta: Number((Math.random() * 2 + 0.5).toFixed(2)),
      eps: Number((Math.random() * 10 + 1).toFixed(2)),
      lastUpdated: new Date().toISOString()
    };
  }

  private generateMockTechnicalIndicators(): TechnicalIndicators {
    return {
      rsi: Number((Math.random() * 100).toFixed(2)),
      macd: {
        macd: Number((Math.random() * 4 - 2).toFixed(3)),
        signal: Number((Math.random() * 4 - 2).toFixed(3)),
        histogram: Number((Math.random() * 2 - 1).toFixed(3))
      },
      sma20: Number((Math.random() * 200 + 50).toFixed(2)),
      sma50: Number((Math.random() * 200 + 50).toFixed(2)),
      sma200: Number((Math.random() * 200 + 50).toFixed(2)),
      ema12: Number((Math.random() * 200 + 50).toFixed(2)),
      ema26: Number((Math.random() * 200 + 50).toFixed(2)),
      bollingerBands: {
        upper: Number((Math.random() * 200 + 100).toFixed(2)),
        middle: Number((Math.random() * 200 + 80).toFixed(2)),
        lower: Number((Math.random() * 200 + 60).toFixed(2))
      },
      stochastic: {
        k: Number((Math.random() * 100).toFixed(2)),
        d: Number((Math.random() * 100).toFixed(2))
      }
    };
  }

  private generateMockFundamentalData(): FundamentalData {
    return {
      revenue: Math.floor(Math.random() * 100000000000), // Up to $100B
      netIncome: Math.floor(Math.random() * 20000000000), // Up to $20B
      totalAssets: Math.floor(Math.random() * 500000000000), // Up to $500B
      totalDebt: Math.floor(Math.random() * 100000000000), // Up to $100B
      freeCashFlow: Math.floor(Math.random() * 30000000000), // Up to $30B
      returnOnEquity: Number((Math.random() * 30).toFixed(2)),
      returnOnAssets: Number((Math.random() * 15).toFixed(2)),
      debtToEquity: Number((Math.random() * 2).toFixed(2)),
      currentRatio: Number((Math.random() * 3 + 0.5).toFixed(2)),
      quickRatio: Number((Math.random() * 2 + 0.3).toFixed(2)),
      grossMargin: Number((Math.random() * 50 + 20).toFixed(2)),
      operatingMargin: Number((Math.random() * 30 + 5).toFixed(2)),
      profitMargin: Number((Math.random() * 25 + 2).toFixed(2)),
      bookValue: Number((Math.random() * 100 + 10).toFixed(2)),
      priceToBook: Number((Math.random() * 5 + 0.5).toFixed(2)),
      priceToSales: Number((Math.random() * 10 + 0.5).toFixed(2)),
      pegRatio: Number((Math.random() * 3 + 0.5).toFixed(2)),
      dividendYield: Number((Math.random() * 6).toFixed(2)),
      payoutRatio: Number((Math.random() * 80 + 10).toFixed(2)),
      earningsGrowth: Number((Math.random() * 40 - 10).toFixed(2)),
      revenueGrowth: Number((Math.random() * 30 - 5).toFixed(2))
    };
  }

  private generateMockNews(ticker: string): NewsItem[] {
    const headlines = [
      `${ticker} Reports Strong Q4 Earnings, Beats Expectations`,
      `Analysts Upgrade ${ticker} Following Strategic Partnership`,
      `${ticker} Announces New Product Launch, Stock Rises`,
      `Market Volatility Affects ${ticker} Trading Volume`,
      `${ticker} CEO Discusses Future Growth Plans in Interview`,
      `Institutional Investors Increase ${ticker} Holdings`,
      `${ticker} Faces Regulatory Scrutiny Over New Policies`,
      `${ticker} Stock Shows Resilience Amid Market Downturn`
    ];

    return headlines.slice(0, 5).map((title, index) => ({
      title,
      summary: `Latest developments regarding ${ticker} and its market performance. This news item provides insights into recent company activities and market reactions.`,
      url: `https://example.com/news/${ticker.toLowerCase()}-${index}`,
      source: ['Reuters', 'Bloomberg', 'CNBC', 'MarketWatch', 'Yahoo Finance'][index % 5],
      publishedAt: new Date(Date.now() - Math.random() * ******** * 7).toISOString(), // Last 7 days
      sentiment: ['positive', 'negative', 'neutral'][Math.floor(Math.random() * 3)] as any,
      relevanceScore: Number((Math.random() * 0.4 + 0.6).toFixed(2)) // 0.6-1.0
    }));
  }

  private generateMockAnalystRatings(ticker: string): AnalystRating[] {
    const firms = ['Goldman Sachs', 'Morgan Stanley', 'JP Morgan', 'Bank of America', 'Citigroup'];
    const ratings: Array<'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell'> = 
      ['Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell'];

    return firms.map(firm => ({
      firm,
      rating: ratings[Math.floor(Math.random() * ratings.length)],
      targetPrice: Number((Math.random() * 200 + 50).toFixed(2)),
      date: new Date(Date.now() - Math.random() * ******** * 30).toISOString() // Last 30 days
    }));
  }

  async getStockData(ticker: string): Promise<StockData> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `stock-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // In production, this would call a real API
      // const response = await fetch(`${this.baseUrl}/stock/${ticker}`);
      // const data = await response.json();
      
      // For now, generate realistic mock data
      const data = this.generateMockStockData(ticker);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching stock data:', error);
      // Return mock data as fallback
      return this.generateMockStockData(ticker);
    }
  }

  async getTechnicalIndicators(ticker: string): Promise<TechnicalIndicators> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `technical-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockTechnicalIndicators();
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching technical indicators:', error);
      return this.generateMockTechnicalIndicators();
    }
  }

  async getFundamentalData(ticker: string): Promise<FundamentalData> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `fundamental-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockFundamentalData();
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching fundamental data:', error);
      return this.generateMockFundamentalData();
    }
  }

  async getNews(ticker: string): Promise<NewsItem[]> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `news-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockNews(ticker);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching news:', error);
      return this.generateMockNews(ticker);
    }
  }

  async getAnalystRatings(ticker: string): Promise<AnalystRating[]> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `ratings-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockAnalystRatings(ticker);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching analyst ratings:', error);
      return this.generateMockAnalystRatings(ticker);
    }
  }

  // Get comprehensive analysis data
  async getComprehensiveAnalysis(ticker: string): Promise<{
    stock: StockData;
    technical: TechnicalIndicators;
    fundamental: FundamentalData;
    news: NewsItem[];
    ratings: AnalystRating[];
  }> {
    const [stock, technical, fundamental, news, ratings] = await Promise.all([
      this.getStockData(ticker),
      this.getTechnicalIndicators(ticker),
      this.getFundamentalData(ticker),
      this.getNews(ticker),
      this.getAnalystRatings(ticker)
    ]);

    return { stock, technical, fundamental, news, ratings };
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache stats
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

export const marketDataService = new MarketDataService();
