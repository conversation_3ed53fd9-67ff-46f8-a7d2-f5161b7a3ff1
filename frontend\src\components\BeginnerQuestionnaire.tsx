import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { 
  User,
  Target,
  DollarSign,
  Clock,
  Shield,
  TrendingUp,
  BookOpen,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Lightbulb,
  AlertCircle
} from 'lucide-react';

export interface UserProfile {
  experienceLevel: 'beginner' | 'intermediate' | 'advanced';
  investmentGoals: string[];
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  timeHorizon: 'short' | 'medium' | 'long';
  monthlyBudget: number;
  hasEmergencyFund: boolean;
  investmentKnowledge: string[];
  preferredSectors: string[];
}

interface BeginnerQuestionnaireProps {
  onComplete: (profile: UserProfile) => void;
  onSkip: () => void;
}

export const BeginnerQuestionnaire: React.FC<BeginnerQuestionnaireProps> = ({
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Partial<UserProfile>>({
    experienceLevel: 'beginner',
    investmentGoals: [],
    riskTolerance: 'moderate',
    timeHorizon: 'medium',
    monthlyBudget: 500,
    hasEmergencyFund: false,
    investmentKnowledge: [],
    preferredSectors: []
  });

  const questions = [
    {
      id: 'experience',
      title: 'Investment Experience',
      subtitle: 'Help us understand your background',
      icon: User,
      component: ExperienceQuestion
    },
    {
      id: 'goals',
      title: 'Investment Goals',
      subtitle: 'What are you hoping to achieve?',
      icon: Target,
      component: GoalsQuestion
    },
    {
      id: 'risk',
      title: 'Risk Tolerance',
      subtitle: 'How comfortable are you with market volatility?',
      icon: Shield,
      component: RiskQuestion
    },
    {
      id: 'timeline',
      title: 'Time Horizon',
      subtitle: 'When do you need your money?',
      icon: Clock,
      component: TimelineQuestion
    },
    {
      id: 'budget',
      title: 'Investment Budget',
      subtitle: 'How much can you invest monthly?',
      icon: DollarSign,
      component: BudgetQuestion
    },
    {
      id: 'knowledge',
      title: 'Investment Knowledge',
      subtitle: 'What areas interest you most?',
      icon: BookOpen,
      component: KnowledgeQuestion
    }
  ];

  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete questionnaire
      const profile: UserProfile = {
        experienceLevel: answers.experienceLevel || 'beginner',
        investmentGoals: answers.investmentGoals || [],
        riskTolerance: answers.riskTolerance || 'moderate',
        timeHorizon: answers.timeHorizon || 'medium',
        monthlyBudget: answers.monthlyBudget || 500,
        hasEmergencyFund: answers.hasEmergencyFund || false,
        investmentKnowledge: answers.investmentKnowledge || [],
        preferredSectors: answers.preferredSectors || []
      };
      onComplete(profile);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateAnswer = (key: keyof UserProfile, value: any) => {
    setAnswers(prev => ({ ...prev, [key]: value }));
  };

  const currentQuestion = questions[currentStep];
  const CurrentComponent = currentQuestion.component;
  const IconComponent = currentQuestion.icon;

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-gray-800 border-gray-700">
        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-blue-600 rounded-full">
                <IconComponent className="h-8 w-8 text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">
              Welcome to Portfolio Tracker
            </h1>
            <p className="text-gray-300 mb-4">
              Let's personalize your investment experience
            </p>
            
            {/* Progress Bar */}
            <div className="flex items-center justify-center space-x-2 mb-6">
              {questions.map((_, index) => (
                <div
                  key={index}
                  className={`h-2 w-8 rounded-full transition-colors ${
                    index <= currentStep ? 'bg-blue-500' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>
            
            <div className="text-sm text-gray-400">
              Step {currentStep + 1} of {questions.length}
            </div>
          </div>

          {/* Question Content */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-white mb-2">
              {currentQuestion.title}
            </h2>
            <p className="text-gray-300 mb-6">
              {currentQuestion.subtitle}
            </p>
            
            <CurrentComponent
              answers={answers}
              updateAnswer={updateAnswer}
            />
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-3">
              {currentStep > 0 && (
                <Button
                  onClick={handlePrevious}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}
              
              <Button
                onClick={onSkip}
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Skip for now
              </Button>
            </div>

            <Button
              onClick={handleNext}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {currentStep === questions.length - 1 ? (
                <>
                  Complete Setup
                  <CheckCircle className="h-4 w-4 ml-2" />
                </>
              ) : (
                <>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Individual Question Components
interface QuestionProps {
  answers: Partial<UserProfile>;
  updateAnswer: (key: keyof UserProfile, value: any) => void;
}

const ExperienceQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const options = [
    {
      value: 'beginner',
      title: 'Complete Beginner',
      description: 'I\'m new to investing and want to learn the basics',
      icon: '🌱'
    },
    {
      value: 'intermediate',
      title: 'Some Experience',
      description: 'I have some knowledge and maybe a few investments',
      icon: '📈'
    },
    {
      value: 'advanced',
      title: 'Experienced Investor',
      description: 'I actively manage investments and understand markets',
      icon: '🎯'
    }
  ];

  return (
    <div className="space-y-3">
      {options.map((option) => (
        <button
          key={option.value}
          onClick={() => updateAnswer('experienceLevel', option.value)}
          className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
            answers.experienceLevel === option.value
              ? 'border-blue-500 bg-blue-500/10'
              : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
          }`}
        >
          <div className="flex items-start space-x-3">
            <span className="text-2xl">{option.icon}</span>
            <div>
              <h3 className="text-white font-semibold">{option.title}</h3>
              <p className="text-gray-300 text-sm">{option.description}</p>
            </div>
          </div>
        </button>
      ))}
      
      {answers.experienceLevel === 'beginner' && (
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <div className="flex items-start space-x-3">
            <Lightbulb className="h-5 w-5 text-blue-400 mt-0.5" />
            <div>
              <h4 className="text-blue-300 font-medium">Perfect! We'll help you get started</h4>
              <p className="text-blue-200 text-sm mt-1">
                We'll provide educational content, simple explanations, and beginner-friendly investment suggestions.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const GoalsQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const goals = [
    { id: 'retirement', label: 'Retirement Planning', icon: '🏖️' },
    { id: 'wealth', label: 'Build Long-term Wealth', icon: '💰' },
    { id: 'income', label: 'Generate Income', icon: '💵' },
    { id: 'education', label: 'Education Funding', icon: '🎓' },
    { id: 'home', label: 'Buy a Home', icon: '🏠' },
    { id: 'emergency', label: 'Emergency Fund', icon: '🛡️' },
    { id: 'travel', label: 'Travel & Experiences', icon: '✈️' },
    { id: 'business', label: 'Start a Business', icon: '🚀' }
  ];

  const toggleGoal = (goalId: string) => {
    const currentGoals = answers.investmentGoals || [];
    const newGoals = currentGoals.includes(goalId)
      ? currentGoals.filter(g => g !== goalId)
      : [...currentGoals, goalId];
    updateAnswer('investmentGoals', newGoals);
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-3 mb-4">
        {goals.map((goal) => (
          <button
            key={goal.id}
            onClick={() => toggleGoal(goal.id)}
            className={`p-3 rounded-lg border-2 text-left transition-all ${
              answers.investmentGoals?.includes(goal.id)
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
            }`}
          >
            <div className="flex items-center space-x-2">
              <span className="text-lg">{goal.icon}</span>
              <span className="text-white text-sm font-medium">{goal.label}</span>
            </div>
          </button>
        ))}
      </div>

      <div className="text-center">
        <p className="text-gray-400 text-sm">
          Select all that apply • {answers.investmentGoals?.length || 0} selected
        </p>
      </div>
    </div>
  );
};

const RiskQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const riskLevels = [
    {
      value: 'conservative',
      title: 'Conservative',
      description: 'I prefer stable, predictable returns even if they\'re lower',
      example: 'Bonds, savings accounts, stable dividend stocks',
      icon: '🛡️',
      color: 'green'
    },
    {
      value: 'moderate',
      title: 'Moderate',
      description: 'I want a balance between growth and stability',
      example: 'Mix of stocks and bonds, diversified portfolio',
      icon: '⚖️',
      color: 'blue'
    },
    {
      value: 'aggressive',
      title: 'Aggressive',
      description: 'I\'m willing to take risks for potentially higher returns',
      example: 'Growth stocks, emerging markets, individual stocks',
      icon: '🚀',
      color: 'red'
    }
  ];

  return (
    <div className="space-y-4">
      {riskLevels.map((level) => (
        <button
          key={level.value}
          onClick={() => updateAnswer('riskTolerance', level.value)}
          className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
            answers.riskTolerance === level.value
              ? `border-${level.color}-500 bg-${level.color}-500/10`
              : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
          }`}
        >
          <div className="flex items-start space-x-3">
            <span className="text-2xl">{level.icon}</span>
            <div className="flex-1">
              <h3 className="text-white font-semibold">{level.title}</h3>
              <p className="text-gray-300 text-sm mb-2">{level.description}</p>
              <p className="text-gray-400 text-xs">Examples: {level.example}</p>
            </div>
          </div>
        </button>
      ))}

      <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-4 w-4 text-yellow-400 mt-0.5" />
          <p className="text-yellow-200 text-sm">
            <strong>Tip:</strong> Your risk tolerance can change over time. Most beginners start with moderate risk.
          </p>
        </div>
      </div>
    </div>
  );
};

const TimelineQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const timelines = [
    {
      value: 'short',
      title: 'Short-term (1-3 years)',
      description: 'I need this money soon',
      suggestion: 'Consider safer, more liquid investments',
      icon: '⏰'
    },
    {
      value: 'medium',
      title: 'Medium-term (3-10 years)',
      description: 'I have some time to let it grow',
      suggestion: 'Balanced approach with moderate risk',
      icon: '📅'
    },
    {
      value: 'long',
      title: 'Long-term (10+ years)',
      description: 'I can wait for long-term growth',
      suggestion: 'Can take more risk for higher potential returns',
      icon: '🗓️'
    }
  ];

  return (
    <div className="space-y-3">
      {timelines.map((timeline) => (
        <button
          key={timeline.value}
          onClick={() => updateAnswer('timeHorizon', timeline.value)}
          className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
            answers.timeHorizon === timeline.value
              ? 'border-blue-500 bg-blue-500/10'
              : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
          }`}
        >
          <div className="flex items-start space-x-3">
            <span className="text-2xl">{timeline.icon}</span>
            <div>
              <h3 className="text-white font-semibold">{timeline.title}</h3>
              <p className="text-gray-300 text-sm">{timeline.description}</p>
              <p className="text-blue-300 text-xs mt-1">{timeline.suggestion}</p>
            </div>
          </div>
        </button>
      ))}
    </div>
  );
};

const BudgetQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const budgetRanges = [
    { value: 100, label: '$100 or less', description: 'Starting small is perfectly fine!' },
    { value: 250, label: '$100 - $500', description: 'A good amount to begin building wealth' },
    { value: 750, label: '$500 - $1,000', description: 'Solid foundation for diversification' },
    { value: 1500, label: '$1,000 - $2,000', description: 'Great potential for growth' },
    { value: 3000, label: '$2,000+', description: 'Excellent opportunity for wealth building' }
  ];

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        {budgetRanges.map((range) => (
          <button
            key={range.value}
            onClick={() => updateAnswer('monthlyBudget', range.value)}
            className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
              answers.monthlyBudget === range.value
                ? 'border-green-500 bg-green-500/10'
                : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
            }`}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-semibold">{range.label}</h3>
                <p className="text-gray-300 text-sm">{range.description}</p>
              </div>
              <DollarSign className="h-5 w-5 text-green-400" />
            </div>
          </button>
        ))}
      </div>

      <div className="mt-4 p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
        <div className="flex items-start space-x-2">
          <Lightbulb className="h-4 w-4 text-green-400 mt-0.5" />
          <div>
            <p className="text-green-200 text-sm">
              <strong>Remember:</strong> Only invest money you can afford to lose. Make sure you have an emergency fund first!
            </p>
          </div>
        </div>
      </div>

      <div className="mt-3">
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={answers.hasEmergencyFund}
            onChange={(e) => updateAnswer('hasEmergencyFund', e.target.checked)}
            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
          />
          <span className="text-white text-sm">
            I have an emergency fund covering 3-6 months of expenses
          </span>
        </label>
      </div>
    </div>
  );
};

const KnowledgeQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const knowledgeAreas = [
    { id: 'stocks', label: 'Individual Stocks', icon: '📈' },
    { id: 'etfs', label: 'ETFs & Index Funds', icon: '📊' },
    { id: 'bonds', label: 'Bonds & Fixed Income', icon: '🏛️' },
    { id: 'crypto', label: 'Cryptocurrency', icon: '₿' },
    { id: 'reits', label: 'Real Estate (REITs)', icon: '🏠' },
    { id: 'commodities', label: 'Commodities', icon: '🥇' },
    { id: 'options', label: 'Options Trading', icon: '⚡' },
    { id: 'international', label: 'International Markets', icon: '🌍' }
  ];

  const sectors = [
    { id: 'technology', label: 'Technology', icon: '💻' },
    { id: 'healthcare', label: 'Healthcare', icon: '🏥' },
    { id: 'finance', label: 'Financial Services', icon: '🏦' },
    { id: 'consumer', label: 'Consumer Goods', icon: '🛍️' },
    { id: 'energy', label: 'Energy', icon: '⚡' },
    { id: 'utilities', label: 'Utilities', icon: '🔌' },
    { id: 'industrials', label: 'Industrials', icon: '🏭' },
    { id: 'materials', label: 'Materials', icon: '⛏️' }
  ];

  const toggleKnowledge = (area: string) => {
    const current = answers.investmentKnowledge || [];
    const updated = current.includes(area)
      ? current.filter(k => k !== area)
      : [...current, area];
    updateAnswer('investmentKnowledge', updated);
  };

  const toggleSector = (sector: string) => {
    const current = answers.preferredSectors || [];
    const updated = current.includes(sector)
      ? current.filter(s => s !== sector)
      : [...current, sector];
    updateAnswer('preferredSectors', updated);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-white font-semibold mb-3">Investment Types I'm Interested In:</h3>
        <div className="grid grid-cols-2 gap-2">
          {knowledgeAreas.map((area) => (
            <button
              key={area.id}
              onClick={() => toggleKnowledge(area.id)}
              className={`p-2 rounded-lg border text-left transition-all ${
                answers.investmentKnowledge?.includes(area.id)
                  ? 'border-blue-500 bg-blue-500/10'
                  : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <span className="text-sm">{area.icon}</span>
                <span className="text-white text-xs">{area.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      <div>
        <h3 className="text-white font-semibold mb-3">Sectors I Find Interesting:</h3>
        <div className="grid grid-cols-2 gap-2">
          {sectors.map((sector) => (
            <button
              key={sector.id}
              onClick={() => toggleSector(sector.id)}
              className={`p-2 rounded-lg border text-left transition-all ${
                answers.preferredSectors?.includes(sector.id)
                  ? 'border-green-500 bg-green-500/10'
                  : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <span className="text-sm">{sector.icon}</span>
                <span className="text-white text-xs">{sector.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>

      <div className="text-center">
        <p className="text-gray-400 text-sm">
          Don't worry if you're not sure - we'll provide educational content for everything!
        </p>
      </div>
    </div>
  );
};
