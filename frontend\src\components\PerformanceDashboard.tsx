import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { PortfolioCharts } from './PortfolioCharts';
import { AIPortfolioInsights } from './AIPortfolioInsights';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Target,
  Calendar,
  DollarSign,
  Percent,
  Activity,
  Award,
  AlertTriangle
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface Portfolio {
  portfolio_id: string;
  name: string;
  total_value: number;
  total_gain_loss: number;
  total_gain_loss_percent: number;
  holdings: PortfolioHolding[];
  cash_balance: number;
  created_date: string;
  last_updated: string;
}

interface PerformanceMetrics {
  totalReturn: number;
  totalReturnPercent: number;
  dayChange: number;
  dayChangePercent: number;
  weekChange: number;
  weekChangePercent: number;
  monthChange: number;
  monthChangePercent: number;
  yearChange: number;
  yearChangePercent: number;
  volatility: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
}

interface PerformanceDashboardProps {
  portfolio: Portfolio;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  portfolio,
  userTier,
  onTokenDeduct
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1D' | '1W' | '1M' | '3M' | '1Y' | 'ALL'>('1M');
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);

  // Demo performance metrics
  useEffect(() => {
    const demoMetrics: PerformanceMetrics = {
      totalReturn: portfolio.total_gain_loss,
      totalReturnPercent: portfolio.total_gain_loss_percent,
      dayChange: 1250.75,
      dayChangePercent: 0.99,
      weekChange: -875.25,
      weekChangePercent: -0.69,
      monthChange: 3420.50,
      monthChangePercent: 2.79,
      yearChange: 8750.50,
      yearChangePercent: 7.48,
      volatility: 18.5,
      sharpeRatio: 1.24,
      maxDrawdown: -12.3,
      winRate: 68.5
    };
    setPerformanceMetrics(demoMetrics);
  }, [portfolio]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  const getSectorAllocation = () => {
    const sectorMap = new Map<string, number>();
    portfolio.holdings.forEach(holding => {
      const current = sectorMap.get(holding.sector) || 0;
      sectorMap.set(holding.sector, current + holding.market_value);
    });
    
    const total = portfolio.holdings.reduce((sum, holding) => sum + holding.market_value, 0);
    return Array.from(sectorMap.entries()).map(([sector, value]) => ({
      sector,
      value,
      percentage: (value / total) * 100
    }));
  };

  const getTopPerformers = () => {
    return portfolio.holdings
      .filter(h => h.unrealized_gain_loss_percent > 0)
      .sort((a, b) => b.unrealized_gain_loss_percent - a.unrealized_gain_loss_percent)
      .slice(0, 5);
  };

  const getWorstPerformers = () => {
    return portfolio.holdings
      .filter(h => h.unrealized_gain_loss_percent < 0)
      .sort((a, b) => a.unrealized_gain_loss_percent - b.unrealized_gain_loss_percent)
      .slice(0, 5);
  };

  const getPortfolioHealthScore = () => {
    if (!performanceMetrics) return 0;
    
    // Simple scoring algorithm based on multiple factors
    let score = 50; // Base score
    
    // Performance factor (40% weight)
    if (performanceMetrics.totalReturnPercent > 10) score += 20;
    else if (performanceMetrics.totalReturnPercent > 5) score += 15;
    else if (performanceMetrics.totalReturnPercent > 0) score += 10;
    else if (performanceMetrics.totalReturnPercent > -5) score += 5;
    else score -= 10;
    
    // Diversification factor (30% weight)
    const sectorCount = getSectorAllocation().length;
    if (sectorCount >= 5) score += 15;
    else if (sectorCount >= 3) score += 10;
    else if (sectorCount >= 2) score += 5;
    
    // Risk factor (30% weight)
    if (performanceMetrics.sharpeRatio > 1.5) score += 15;
    else if (performanceMetrics.sharpeRatio > 1.0) score += 10;
    else if (performanceMetrics.sharpeRatio > 0.5) score += 5;
    
    return Math.min(Math.max(score, 0), 100);
  };

  const timeframes = [
    { key: '1D', label: '1 Day' },
    { key: '1W', label: '1 Week' },
    { key: '1M', label: '1 Month' },
    { key: '3M', label: '3 Months' },
    { key: '1Y', label: '1 Year' },
    { key: 'ALL', label: 'All Time' }
  ];

  const sectorAllocation = getSectorAllocation();
  const topPerformers = getTopPerformers();
  const worstPerformers = getWorstPerformers();
  const healthScore = getPortfolioHealthScore();

  if (!performanceMetrics) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Interactive Charts */}
      <PortfolioCharts portfolio={portfolio} userTier={userTier} />

      {/* Performance Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
            Performance Overview
          </h3>
          
          {/* Timeframe Selector */}
          <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
            {timeframes.map((timeframe) => (
              <button
                key={timeframe.key}
                onClick={() => setSelectedTimeframe(timeframe.key as any)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  selectedTimeframe === timeframe.key
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                {timeframe.label}
              </button>
            ))}
          </div>
        </div>

        {/* Performance Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <DollarSign className="h-5 w-5 text-green-400" />
            </div>
            <div className="text-2xl font-bold text-white">
              {formatCurrency(performanceMetrics.totalReturn)}
            </div>
            <div className={`text-sm ${performanceMetrics.totalReturnPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(performanceMetrics.totalReturnPercent)}
            </div>
            <div className="text-xs text-gray-400 mt-1">Total Return</div>
          </div>

          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Activity className="h-5 w-5 text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-white">
              {performanceMetrics.volatility.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-400 mt-1">Volatility</div>
          </div>

          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Award className="h-5 w-5 text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-white">
              {performanceMetrics.sharpeRatio.toFixed(2)}
            </div>
            <div className="text-xs text-gray-400 mt-1">Sharpe Ratio</div>
          </div>

          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="flex items-center justify-center mb-2">
              <Percent className="h-5 w-5 text-orange-400" />
            </div>
            <div className="text-2xl font-bold text-white">
              {performanceMetrics.winRate.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-400 mt-1">Win Rate</div>
          </div>
        </div>
      </Card>

      {/* Performance by Timeframe */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Day Change</p>
              <p className={`text-lg font-bold ${performanceMetrics.dayChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {formatCurrency(performanceMetrics.dayChange)}
              </p>
              <p className={`text-xs ${performanceMetrics.dayChangePercent >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {formatPercent(performanceMetrics.dayChangePercent)}
              </p>
            </div>
            <Calendar className="h-6 w-6 text-green-400" />
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-blue-900/20 to-cyan-900/20 border-blue-500/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Week Change</p>
              <p className={`text-lg font-bold ${performanceMetrics.weekChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {formatCurrency(performanceMetrics.weekChange)}
              </p>
              <p className={`text-xs ${performanceMetrics.weekChangePercent >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {formatPercent(performanceMetrics.weekChangePercent)}
              </p>
            </div>
            <Calendar className="h-6 w-6 text-blue-400" />
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-900/20 to-pink-900/20 border-purple-500/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Month Change</p>
              <p className={`text-lg font-bold ${performanceMetrics.monthChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {formatCurrency(performanceMetrics.monthChange)}
              </p>
              <p className={`text-xs ${performanceMetrics.monthChangePercent >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {formatPercent(performanceMetrics.monthChangePercent)}
              </p>
            </div>
            <Calendar className="h-6 w-6 text-purple-400" />
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-900/20 to-red-900/20 border-orange-500/30">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Year Change</p>
              <p className={`text-lg font-bold ${performanceMetrics.yearChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {formatCurrency(performanceMetrics.yearChange)}
              </p>
              <p className={`text-xs ${performanceMetrics.yearChangePercent >= 0 ? 'text-green-300' : 'text-red-300'}`}>
                {formatPercent(performanceMetrics.yearChangePercent)}
              </p>
            </div>
            <Calendar className="h-6 w-6 text-orange-400" />
          </div>
        </Card>
      </div>

      {/* Sector Allocation & Top/Worst Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sector Allocation */}
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
            <Target className="h-5 w-5 mr-2 text-blue-400" />
            Sector Allocation
          </h3>
          <div className="space-y-3">
            {sectorAllocation.map((sector, index) => (
              <div key={sector.sector} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{
                        background: `hsl(${(index * 60) % 360}, 70%, 50%)`
                      }}
                    />
                    <span className="text-white font-medium">{sector.sector}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-white font-semibold text-sm">
                      {formatCurrency(sector.value)}
                    </div>
                    <div className="text-gray-300 text-xs">
                      {sector.percentage.toFixed(1)}%
                    </div>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${sector.percentage}%`,
                      background: `hsl(${(index * 60) % 360}, 70%, 50%)`
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Top & Worst Performers */}
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
            Performance Leaders
          </h3>
          
          {/* Top Performers */}
          <div className="mb-6">
            <h4 className="text-lg font-medium text-white mb-3 flex items-center">
              <Award className="h-4 w-4 mr-2 text-green-400" />
              Top Performers
            </h4>
            <div className="space-y-2">
              {topPerformers.slice(0, 3).map((holding) => (
                <div key={holding.ticker} className="flex items-center justify-between p-2 bg-green-900/20 rounded-lg border border-green-500/30">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-white">{holding.ticker}</span>
                    <Badge className="bg-green-600 text-white text-xs">
                      {formatPercent(holding.unrealized_gain_loss_percent)}
                    </Badge>
                  </div>
                  <span className="text-green-400 font-medium text-sm">
                    {formatCurrency(holding.unrealized_gain_loss)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Worst Performers */}
          <div>
            <h4 className="text-lg font-medium text-white mb-3 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-red-400" />
              Needs Attention
            </h4>
            <div className="space-y-2">
              {worstPerformers.slice(0, 3).map((holding) => (
                <div key={holding.ticker} className="flex items-center justify-between p-2 bg-red-900/20 rounded-lg border border-red-500/30">
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-white">{holding.ticker}</span>
                    <Badge className="bg-red-600 text-white text-xs">
                      {formatPercent(holding.unrealized_gain_loss_percent)}
                    </Badge>
                  </div>
                  <span className="text-red-400 font-medium text-sm">
                    {formatCurrency(holding.unrealized_gain_loss)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>

      {/* Portfolio Health Score */}
      <Card className="p-6 bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-500/30">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-400" />
            Portfolio Health Score
          </h3>
          <Badge className={`text-white px-3 py-1 ${
            healthScore >= 80 ? 'bg-green-600' :
            healthScore >= 60 ? 'bg-blue-600' :
            healthScore >= 40 ? 'bg-yellow-600' : 'bg-red-600'
          }`}>
            {healthScore}/100
          </Badge>
        </div>
        
        <div className="mb-4">
          <div className="w-full bg-gray-700 rounded-full h-4">
            <div
              className={`h-4 rounded-full transition-all duration-500 ${
                healthScore >= 80 ? 'bg-green-500' :
                healthScore >= 60 ? 'bg-blue-500' :
                healthScore >= 40 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${healthScore}%` }}
            />
          </div>
        </div>
        
        <p className="text-gray-300 text-sm mb-4">
          Based on performance, diversification, and risk metrics. 
          {healthScore >= 80 && ' Excellent portfolio health!'}
          {healthScore >= 60 && healthScore < 80 && ' Good portfolio performance with room for improvement.'}
          {healthScore >= 40 && healthScore < 60 && ' Consider rebalancing and diversification.'}
          {healthScore < 40 && ' Portfolio needs attention - consider professional advice.'}
        </p>

        {userTier === 'Pro' && (
          <Button 
            className="bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => onTokenDeduct(150)}
          >
            <Target className="h-4 w-4 mr-2" />
            Get Detailed Health Report (150 tokens)
          </Button>
        )}
      </Card>

      {/* AI-Powered Insights */}
      <AIPortfolioInsights
        portfolio={portfolio}
        userTier={userTier}
        onTokenDeduct={onTokenDeduct}
      />
    </div>
  );
};
